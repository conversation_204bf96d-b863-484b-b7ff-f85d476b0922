# UnifiedLoan

Borrow or repay
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Currency | 
**type** | **str** | type: borrow - borrow, repay - repay | 
**amount** | **str** | The amount of lending or repaying | 
**repaid_all** | **bool** | Full repayment is solely for repayment operations. When set to &#39;true,&#39; it overrides the &#39;amount,&#39; allowing for direct full repayment. | [optional] 
**text** | **str** | User defined custom ID | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


