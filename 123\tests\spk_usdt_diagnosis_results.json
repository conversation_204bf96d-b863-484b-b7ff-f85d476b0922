{"diagnosis_results": {"global_exchanges_check": true, "set_global_exchanges_check": false, "trading_rules_preloader_check": true, "temporary_instance_check": true, "api_keys_check": true, "initialize_all_systems_check": true, "spk_usdt_rule_test": false}, "details": {"global_exchanges": {"initial_state": "None", "set_works": true, "functions_available": true}, "initialize_all_systems": {"method_exists": true, "has_set_call": true, "set_call_lines": [13], "total_lines": 135}, "trading_rules_preloader": {"preloader_available": true, "get_method_exists": true, "spk_rule_exists": true, "rule_details": "TradingRule(symbol='SPK-USDT', exchange='gate', market_type='spot', qty_step=Decimal('0.0001'), price_step=Decimal('0.01'), qty_precision=4, price_precision=4, min_qty=Decimal('0.0001'), max_qty=Decimal('1000000'), min_notional=Decimal('1.0'), source='default', timestamp=1753874809.2726076)"}, "temporary_instance": {"method_exists": true, "creation_results": {"gate": true, "bybit": true, "okx": true}, "success_count": 3, "total_count": 3}, "api_keys": {"configured_count": 7, "total_count": 7, "keys_status": {"GATE_API_KEY": true, "GATE_API_SECRET": true, "BYBIT_API_KEY": true, "BYBIT_API_SECRET": true, "OKX_API_KEY": true, "OKX_API_SECRET": true, "OKX_API_PASSPHRASE": true}}}, "success_rate": 71.42857142857143, "passed_count": 5, "total_count": 7}