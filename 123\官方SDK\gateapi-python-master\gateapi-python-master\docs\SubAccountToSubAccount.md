# SubAccountToSubAccount

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Transfer currency name | 
**sub_account_type** | **str** | Transfer from the account. (deprecate, use &#x60;sub_account_from_type&#x60; and &#x60;sub_account_to_type&#x60; instead) | [optional] 
**sub_account_from** | **str** | Transfer from the user id of the sub-account | 
**sub_account_from_type** | **str** | The sub-account&#39;s outgoing trading account, spot - spot account, futures - perpetual contract account, delivery - delivery contract account. | 
**sub_account_to** | **str** | Transfer to the user id of the sub-account | 
**sub_account_to_type** | **str** | The sub-account&#39;s incoming trading account, spot - spot account, futures - perpetual contract account, delivery - delivery contract account | 
**amount** | **str** | Transfer amount | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


