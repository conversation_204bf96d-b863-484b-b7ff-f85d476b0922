# SubAccount

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**remark** | **str** | custom text | [optional] 
**login_name** | **str** | Sub-account login name: Only letters, numbers and underscores are supported, and cannot contain other illegal characters | 
**password** | **str** | The sub-account&#39;s password. (Default: the same as main account&#39;s password) | [optional] 
**email** | **str** | The sub-account&#39;s email address. (Default: the same as main account&#39;s email address) | [optional] 
**state** | **int** | State: 1-normal, 2-locked\&quot; | [optional] [readonly] 
**type** | **int** | \&quot;Sub-account type: 1 - sub-account, 3 - cross margin account | [optional] [readonly] 
**user_id** | **int** | The user id of the sub-account | [optional] [readonly] 
**create_time** | **int** | Created time | [optional] [readonly] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


