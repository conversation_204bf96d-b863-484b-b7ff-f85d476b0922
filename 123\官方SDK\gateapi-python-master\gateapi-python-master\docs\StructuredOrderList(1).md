# StructuredOrderList

Structured order
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Order ID | [optional] 
**pid** | **str** | Plan ID | [optional] 
**lock_coin** | **str** | Locked coin | [optional] 
**amount** | **str** | Locked amount | [optional] 
**status** | **str** | Status:   SUCCESS - SUCCESS  FAILED - FAILED DONE - DONE | [optional] 
**income** | **str** | Income | [optional] 
**create_time** | **int** | Created time | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


