# RepayCurrencyRes

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**succeeded** | **bool** | Has the repayment been successful | [optional] 
**label** | **str** | Error identifier for unsuccessful operations; empty for successful. | [optional] 
**message** | **str** | Error description in case of operation failure; empty when successful. | [optional] 
**currency** | **str** | Repayment currency | [optional] 
**repaid_principal** | **str** | Principal | [optional] 
**repaid_interest** | **str** | Principal | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


