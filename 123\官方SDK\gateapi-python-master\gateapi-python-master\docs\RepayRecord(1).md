# RepayRecord

Repayment record
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**order_id** | **int** | Order ID | [optional] 
**record_id** | **int** | Repayment record ID | [optional] 
**repaid_amount** | **str** | Repayment amount | [optional] 
**borrow_currency** | **str** | Borrowed currency | [optional] 
**collateral_currency** | **str** | Collateral | [optional] 
**init_ltv** | **str** | The initial collateralization rate | [optional] 
**borrow_time** | **int** | Borrowing time, timestamp | [optional] 
**repay_time** | **int** | Repayment time, timestamp | [optional] 
**total_interest** | **str** | Total interest | [optional] 
**before_left_principal** | **str** | Principal to be repaid before repayment | [optional] 
**after_left_principal** | **str** | Principal to be repaid after repayment | [optional] 
**before_left_collateral** | **str** | Collateral quantity before repayment | [optional] 
**after_left_collateral** | **str** | Collateral quantity after repayment | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


