#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPK-USDT_gate_spot交易规则获取失败问题修复 - 机构级别质量测试
2025-07-30 创建

机构级别测试验证修复的完整性：
1. 系统稳定性测试
2. 错误处理测试  
3. 性能测试
4. 通用性测试
5. 压力测试
6. 边界条件测试

确保100%修复质量，符合机构级别标准
"""

import os
import sys
import asyncio
import time
import json
import concurrent.futures
from typing import Dict, Any, List, Optional
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*70}")
    print(f"🏛️ {title}")
    print('='*70)

def print_result(test_name: str, result: bool, details: str = "", performance: str = ""):
    """打印测试结果"""
    status = "✅ 通过" if result else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")
    if performance:
        print(f"   性能: {performance}")

class InstitutionalQualitySPKUSDTFixTest:
    """机构级别SPK-USDT交易规则修复质量测试"""
    
    def __init__(self):
        self.test_results = {
            "system_stability": False,
            "error_handling": False,
            "performance": False,
            "universality": False,
            "stress_test": False,
            "boundary_conditions": False
        }
        self.performance_metrics = {}
        self.details = {}
    
    async def test_system_stability(self) -> bool:
        """系统稳定性测试"""
        print_section("系统稳定性测试")
        
        try:
            from core.trading_system_initializer import get_trading_system_initializer, get_global_exchanges, set_global_exchanges
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            stability_tests = []
            
            # 测试1: 重复初始化稳定性
            for i in range(5):
                try:
                    initializer = get_trading_system_initializer()
                    mock_exchanges = {
                        'gate': type('MockGateExchange', (), {'name': 'gate'}),
                        'bybit': type('MockBybitExchange', (), {'name': 'bybit'}),
                        'okx': type('MockOKXExchange', (), {'name': 'okx'})
                    }
                    set_global_exchanges(mock_exchanges)
                    global_ex = get_global_exchanges()
                    stability_tests.append(global_ex is not None and len(global_ex) == 3)
                except Exception:
                    stability_tests.append(False)
            
            stability_rate = sum(stability_tests) / len(stability_tests) * 100
            stability_pass = stability_rate >= 95  # 95%稳定性要求
            
            print_result("重复初始化稳定性", stability_pass, f"稳定性: {stability_rate:.1f}%")
            
            # 测试2: 交易规则获取稳定性
            preloader = get_trading_rules_preloader()
            rule_tests = []
            
            for i in range(10):
                try:
                    rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
                    rule_tests.append(rule is not None)
                except Exception:
                    rule_tests.append(False)
            
            rule_stability = sum(rule_tests) / len(rule_tests) * 100
            rule_pass = rule_stability >= 95
            
            print_result("交易规则获取稳定性", rule_pass, f"稳定性: {rule_stability:.1f}%")
            
            # 测试3: 内存泄漏检测
            import gc
            initial_objects = len(gc.get_objects())
            
            for i in range(100):
                rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            
            gc.collect()
            final_objects = len(gc.get_objects())
            memory_growth = final_objects - initial_objects
            memory_pass = memory_growth < 1000  # 内存增长小于1000个对象
            
            print_result("内存泄漏检测", memory_pass, f"对象增长: {memory_growth}")
            
            overall_stability = stability_pass and rule_pass and memory_pass
            
            self.details["system_stability"] = {
                "initialization_stability": stability_rate,
                "rule_stability": rule_stability,
                "memory_growth": memory_growth,
                "overall_pass": overall_stability
            }
            
            return overall_stability
            
        except Exception as e:
            print_result("系统稳定性测试", False, f"错误: {e}")
            self.details["system_stability"] = {"error": str(e)}
            return False
    
    async def test_error_handling(self) -> bool:
        """错误处理测试"""
        print_section("错误处理测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import set_global_exchanges, get_global_exchanges
            
            preloader = get_trading_rules_preloader()
            error_tests = []
            
            # 测试1: 无效交易所处理
            try:
                rule = preloader.get_trading_rule("invalid_exchange", "SPK-USDT", "spot")
                # 应该返回None或默认规则，不应该抛出异常
                error_tests.append(True)
            except Exception:
                error_tests.append(False)
            
            # 测试2: 无效交易对处理
            try:
                rule = preloader.get_trading_rule("gate", "INVALID-PAIR", "spot")
                error_tests.append(True)
            except Exception:
                error_tests.append(False)
            
            # 测试3: 无效市场类型处理
            try:
                rule = preloader.get_trading_rule("gate", "SPK-USDT", "invalid_market")
                error_tests.append(True)
            except Exception:
                error_tests.append(False)
            
            # 测试4: 全局交易所实例为None时的处理
            original_exchanges = get_global_exchanges()
            set_global_exchanges(None)
            try:
                rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
                # 应该使用临时实例创建机制
                error_tests.append(rule is not None)
            except Exception:
                error_tests.append(False)
            finally:
                set_global_exchanges(original_exchanges)
            
            # 测试5: 空字符串参数处理
            try:
                rule = preloader.get_trading_rule("", "", "")
                error_tests.append(True)
            except Exception:
                error_tests.append(False)
            
            error_handling_rate = sum(error_tests) / len(error_tests) * 100
            error_pass = error_handling_rate >= 80  # 80%错误处理要求
            
            print_result("错误处理能力", error_pass, f"处理率: {error_handling_rate:.1f}%")
            
            self.details["error_handling"] = {
                "error_handling_rate": error_handling_rate,
                "tests_passed": sum(error_tests),
                "total_tests": len(error_tests)
            }
            
            return error_pass
            
        except Exception as e:
            print_result("错误处理测试", False, f"错误: {e}")
            self.details["error_handling"] = {"error": str(e)}
            return False
    
    async def test_performance(self) -> bool:
        """性能测试"""
        print_section("性能测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试1: 单次获取性能
            start_time = time.time()
            rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            single_time = (time.time() - start_time) * 1000
            single_pass = single_time < 50  # 50ms以内
            
            print_result("单次获取性能", single_pass, f"耗时: {single_time:.2f}ms")
            
            # 测试2: 批量获取性能
            test_pairs = [
                ("gate", "SPK-USDT", "spot"),
                ("gate", "SPK-USDT", "futures"),
                ("bybit", "SPK-USDT", "spot"),
                ("bybit", "SPK-USDT", "futures"),
                ("okx", "SPK-USDT", "spot")
            ]
            
            start_time = time.time()
            for exchange, symbol, market_type in test_pairs:
                rule = preloader.get_trading_rule(exchange, symbol, market_type)
            batch_time = (time.time() - start_time) * 1000
            avg_time = batch_time / len(test_pairs)
            batch_pass = avg_time < 30  # 平均30ms以内
            
            print_result("批量获取性能", batch_pass, f"平均耗时: {avg_time:.2f}ms")
            
            # 测试3: 并发获取性能
            async def concurrent_get():
                return preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            
            start_time = time.time()
            tasks = [concurrent_get() for _ in range(10)]
            await asyncio.gather(*tasks)
            concurrent_time = (time.time() - start_time) * 1000
            concurrent_avg = concurrent_time / 10
            concurrent_pass = concurrent_avg < 20  # 并发平均20ms以内
            
            print_result("并发获取性能", concurrent_pass, f"并发平均: {concurrent_avg:.2f}ms")
            
            # 测试4: 缓存命中性能
            # 第一次获取（缓存未命中）
            start_time = time.time()
            rule1 = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            first_time = (time.time() - start_time) * 1000
            
            # 第二次获取（缓存命中）
            start_time = time.time()
            rule2 = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            cache_time = (time.time() - start_time) * 1000
            
            cache_improvement = first_time - cache_time
            cache_pass = cache_time < 5 and cache_improvement > 0  # 缓存命中5ms以内
            
            print_result("缓存命中性能", cache_pass, f"缓存耗时: {cache_time:.2f}ms, 提升: {cache_improvement:.2f}ms")
            
            overall_performance = single_pass and batch_pass and concurrent_pass and cache_pass
            
            self.performance_metrics = {
                "single_time_ms": single_time,
                "batch_avg_time_ms": avg_time,
                "concurrent_avg_time_ms": concurrent_avg,
                "cache_time_ms": cache_time,
                "cache_improvement_ms": cache_improvement
            }
            
            self.details["performance"] = {
                "single_pass": single_pass,
                "batch_pass": batch_pass,
                "concurrent_pass": concurrent_pass,
                "cache_pass": cache_pass,
                "overall_pass": overall_performance,
                "metrics": self.performance_metrics
            }
            
            return overall_performance
            
        except Exception as e:
            print_result("性能测试", False, f"错误: {e}")
            self.details["performance"] = {"error": str(e)}
            return False
    
    async def test_universality(self) -> bool:
        """通用性测试"""
        print_section("通用性测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试所有交易所和市场类型的组合
            exchanges = ["gate", "bybit", "okx"]
            symbols = ["SPK-USDT", "RESOLV-USDT", "ICNT-USDT"]
            market_types = ["spot", "futures"]
            
            total_tests = 0
            successful_tests = 0
            
            for exchange in exchanges:
                for symbol in symbols:
                    for market_type in market_types:
                        total_tests += 1
                        try:
                            rule = preloader.get_trading_rule(exchange, symbol, market_type)
                            if rule is not None:
                                successful_tests += 1
                                print_result(f"  {exchange}_{symbol}_{market_type}", True, "规则获取成功")
                            else:
                                print_result(f"  {exchange}_{symbol}_{market_type}", False, "规则为None")
                        except Exception as e:
                            print_result(f"  {exchange}_{symbol}_{market_type}", False, f"错误: {e}")
            
            universality_rate = (successful_tests / total_tests) * 100
            universality_pass = universality_rate >= 85  # 85%通用性要求
            
            print_result("通用性测试", universality_pass, f"成功率: {universality_rate:.1f}% ({successful_tests}/{total_tests})")
            
            self.details["universality"] = {
                "success_rate": universality_rate,
                "successful_tests": successful_tests,
                "total_tests": total_tests
            }
            
            return universality_pass
            
        except Exception as e:
            print_result("通用性测试", False, f"错误: {e}")
            self.details["universality"] = {"error": str(e)}
            return False
    
    async def test_stress_conditions(self) -> bool:
        """压力测试"""
        print_section("压力测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            
            preloader = get_trading_rules_preloader()
            
            # 测试1: 高频获取压力测试
            start_time = time.time()
            success_count = 0
            
            for i in range(1000):
                try:
                    rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
                    if rule is not None:
                        success_count += 1
                except Exception:
                    pass
            
            stress_time = time.time() - start_time
            stress_rate = (success_count / 1000) * 100
            stress_pass = stress_rate >= 95 and stress_time < 10  # 95%成功率，10秒内完成
            
            print_result("高频获取压力测试", stress_pass, 
                        f"成功率: {stress_rate:.1f}%, 耗时: {stress_time:.2f}s")
            
            # 测试2: 内存压力测试
            import gc
            initial_memory = len(gc.get_objects())
            
            # 创建大量规则获取请求
            for i in range(500):
                for exchange in ["gate", "bybit", "okx"]:
                    rule = preloader.get_trading_rule(exchange, "SPK-USDT", "spot")
            
            gc.collect()
            final_memory = len(gc.get_objects())
            memory_increase = final_memory - initial_memory
            memory_pass = memory_increase < 5000  # 内存增长小于5000个对象
            
            print_result("内存压力测试", memory_pass, f"内存增长: {memory_increase}个对象")
            
            overall_stress = stress_pass and memory_pass
            
            self.details["stress_test"] = {
                "high_frequency_success_rate": stress_rate,
                "high_frequency_time": stress_time,
                "memory_increase": memory_increase,
                "overall_pass": overall_stress
            }
            
            return overall_stress
            
        except Exception as e:
            print_result("压力测试", False, f"错误: {e}")
            self.details["stress_test"] = {"error": str(e)}
            return False
    
    async def test_boundary_conditions(self) -> bool:
        """边界条件测试"""
        print_section("边界条件测试")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            from core.trading_system_initializer import set_global_exchanges, get_global_exchanges
            
            preloader = get_trading_rules_preloader()
            boundary_tests = []
            
            # 保存原始状态
            original_exchanges = get_global_exchanges()
            
            # 测试1: 空全局交易所实例
            set_global_exchanges({})
            try:
                rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
                boundary_tests.append(True)  # 应该能处理空字典
            except Exception:
                boundary_tests.append(False)
            
            # 测试2: None全局交易所实例
            set_global_exchanges(None)
            try:
                rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
                boundary_tests.append(True)  # 应该使用临时实例
            except Exception:
                boundary_tests.append(False)
            
            # 恢复原始状态
            set_global_exchanges(original_exchanges)
            
            # 测试3: 极长交易对名称
            try:
                long_symbol = "A" * 100 + "-USDT"
                rule = preloader.get_trading_rule("gate", long_symbol, "spot")
                boundary_tests.append(True)
            except Exception:
                boundary_tests.append(False)
            
            # 测试4: 特殊字符交易对
            try:
                special_symbol = "SPK@#$-USDT"
                rule = preloader.get_trading_rule("gate", special_symbol, "spot")
                boundary_tests.append(True)
            except Exception:
                boundary_tests.append(False)
            
            # 测试5: 大小写敏感性
            try:
                rule1 = preloader.get_trading_rule("GATE", "SPK-USDT", "SPOT")
                rule2 = preloader.get_trading_rule("gate", "spk-usdt", "spot")
                boundary_tests.append(True)
            except Exception:
                boundary_tests.append(False)
            
            boundary_rate = sum(boundary_tests) / len(boundary_tests) * 100
            boundary_pass = boundary_rate >= 80  # 80%边界条件处理要求
            
            print_result("边界条件测试", boundary_pass, f"处理率: {boundary_rate:.1f}%")
            
            self.details["boundary_conditions"] = {
                "boundary_handling_rate": boundary_rate,
                "tests_passed": sum(boundary_tests),
                "total_tests": len(boundary_tests)
            }
            
            return boundary_pass
            
        except Exception as e:
            print_result("边界条件测试", False, f"错误: {e}")
            self.details["boundary_conditions"] = {"error": str(e)}
            return False
    
    async def run_institutional_quality_test(self) -> Dict[str, Any]:
        """运行机构级别质量测试"""
        print_section("SPK-USDT交易规则修复 - 机构级别质量测试")
        print("验证修复的完整性和机构级别质量标准")
        
        start_time = time.time()
        
        # 执行所有测试
        self.test_results["system_stability"] = await self.test_system_stability()
        self.test_results["error_handling"] = await self.test_error_handling()
        self.test_results["performance"] = await self.test_performance()
        self.test_results["universality"] = await self.test_universality()
        self.test_results["stress_test"] = await self.test_stress_conditions()
        self.test_results["boundary_conditions"] = await self.test_boundary_conditions()
        
        # 综合评估
        print_section("机构级别质量测试结果")
        
        passed_count = sum(self.test_results.values())
        total_count = len(self.test_results)
        quality_score = (passed_count / total_count) * 100
        
        print(f"📊 机构级别质量评估:")
        print(f"   通过测试: {passed_count}/{total_count}")
        print(f"   质量评分: {quality_score:.1f}%")
        print(f"   测试耗时: {time.time() - start_time:.2f}秒")
        
        # 质量等级评定
        if quality_score >= 95:
            quality_grade = "AAA级 (机构级别)"
            quality_status = "🏆 卓越"
        elif quality_score >= 85:
            quality_grade = "AA级 (企业级别)"
            quality_status = "🥇 优秀"
        elif quality_score >= 75:
            quality_grade = "A级 (专业级别)"
            quality_status = "🥈 良好"
        else:
            quality_grade = "B级 (基础级别)"
            quality_status = "🥉 需要改进"
        
        print(f"\n🎯 质量等级: {quality_grade}")
        print(f"🏅 质量状态: {quality_status}")
        
        # 性能指标总结
        if self.performance_metrics:
            print(f"\n⚡ 性能指标:")
            print(f"   单次获取: {self.performance_metrics.get('single_time_ms', 0):.2f}ms")
            print(f"   批量平均: {self.performance_metrics.get('batch_avg_time_ms', 0):.2f}ms")
            print(f"   并发平均: {self.performance_metrics.get('concurrent_avg_time_ms', 0):.2f}ms")
            print(f"   缓存命中: {self.performance_metrics.get('cache_time_ms', 0):.2f}ms")
        
        return {
            "test_results": self.test_results,
            "details": self.details,
            "quality_score": quality_score,
            "quality_grade": quality_grade,
            "quality_status": quality_status,
            "performance_metrics": self.performance_metrics,
            "passed_count": passed_count,
            "total_count": total_count
        }

async def main():
    """主函数"""
    test = InstitutionalQualitySPKUSDTFixTest()
    results = await test.run_institutional_quality_test()
    
    # 保存测试结果
    output_file = "123/tests/institutional_quality_spk_usdt_fix_test_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📄 机构级别质量测试结果已保存到: {output_file}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
