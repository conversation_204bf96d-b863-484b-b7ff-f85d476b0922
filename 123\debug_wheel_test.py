#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试无造轮子测试
"""

import sys
import os
sys.path.append('.')

import inspect
from core.trading_system_initializer import get_trading_system_initializer

def debug_wheel_test():
    """调试无造轮子测试"""
    print("=== 调试无造轮子测试 ===")
    
    try:
        # 获取方法源码
        source = inspect.getsource(get_trading_system_initializer().initialize_trading_system)
        
        print(f"方法源码长度: {len(source)} 字符")
        print(f"使用set_global_exchanges: {'set_global_exchanges' in source}")
        print(f"_global_exchanges出现次数: {source.count('_global_exchanges')}")
        print(f"无重复实现: {source.count('_global_exchanges') == 0}")
        
        # 显示包含set_global_exchanges的行
        lines = source.split('\n')
        for i, line in enumerate(lines):
            if 'set_global_exchanges' in line:
                print(f"第{i+1}行: {line.strip()}")
        
        # 显示包含_global_exchanges的行（如果有）
        for i, line in enumerate(lines):
            if '_global_exchanges' in line:
                print(f"第{i+1}行包含_global_exchanges: {line.strip()}")
        
        # 最终结果
        uses_existing_function = "set_global_exchanges" in source
        no_duplicate_global = source.count("_global_exchanges") == 0
        no_wheel_reinvention = uses_existing_function and no_duplicate_global
        
        print(f"\n最终结果:")
        print(f"  使用现有函数: {uses_existing_function}")
        print(f"  无重复全局变量: {no_duplicate_global}")
        print(f"  无造轮子: {no_wheel_reinvention}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_wheel_test()
