# StructuredGetProjectList

Structured Products
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | Plan ID | [optional] 
**type** | **str** | product type:   &#x60;SharkFin2.0&#x60;-Shark Fin2.0  &#x60;BullishSharkFin&#x60;-Bullish Shark Fin  &#x60;BearishSharkFin&#x60;-Bearish Shark Fin &#x60;DoubleNoTouch&#x60;-Double No-Touch &#x60;RangeAccrual&#x60;-Range Accrual &#x60;SnowBall&#x60;-Snow Ball | [optional] 
**name_en** | **str** | name | [optional] 
**investment_coin** | **str** | Investment Currency | [optional] 
**investment_period** | **str** | Investment term | [optional] 
**min_annual_rate** | **str** | Minimum annual rate | [optional] 
**mid_annual_rate** | **str** | Intermediate annual rate | [optional] 
**max_annual_rate** | **str** | Maximum annual rate | [optional] 
**watch_market** | **str** | Watch market | [optional] 
**start_time** | **int** | start time | [optional] 
**end_time** | **int** | Finished time | [optional] 
**status** | **str** | Status:   &#x60;in_process&#x60;-in progress  &#x60;will_begin&#x60;-will begin  &#x60;wait_settlement&#x60;-waiting for settlement  &#x60;done&#x60;-done | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


