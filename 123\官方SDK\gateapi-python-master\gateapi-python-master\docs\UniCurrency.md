# UniCurrency

Currency detail
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Currency name | [optional] [readonly] 
**min_lend_amount** | **str** | The minimum lending amount, in the unit of the currency. | [optional] [readonly] 
**max_lend_amount** | **str** | The total maximum lending amount, in USDT | [optional] [readonly] 
**max_rate** | **str** | Maximum rate (Hourly) | [optional] [readonly] 
**min_rate** | **str** | Minimum rate (Hourly) | [optional] [readonly] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


